// Nahjul Balagha Data Structure
const nahju<PERSON>BalaghData = {
  sermons: [
    {
      id: 1,
      type: "sermon",
      number: 1,
      title: "خطبہ شقشقیہ",
      arabic: "أَمَا وَاللَّهِ لَقَدْ تَقَمَّصَهَا فُلَانٌ، وَإِنَّهُ لَيَعْلَمُ أَنَّ مَحَلِّي مِنْهَا مَحَلُّ الْقُطْبِ مِنَ الرَّحَى، يَنْحَدِرُ عَنِّي السَّيْلُ، وَلَا يَرْقَى إِلَيَّ الطَّيْرُ",
      english: "By Allah, so-and-so has certainly put on the caliphate like a garment, although he definitely knew that my position in relation to it was the same as the position of the axis in relation to the hand-mill. The flood water flows down from me and the bird cannot fly up to me.",
      urdu: "خدا کی قسم! فلاں شخص نے خلافت کو اس طرح پہن لیا جیسے کپڑا پہنا جاتا ہے، حالانکہ وہ جانتا تھا کہ میری خلافت کے سلسلے میں وہی حیثیت ہے جو چکی کے درمیان میں موجود محور کی ہوتی ہے۔ سیلاب کا پانی مجھ سے نیچے کی طرف بہتا ہے اور پرندہ میری بلندی تک نہیں پہنچ سکتا۔",
      category: "governance",
      tags: ["leadership", "justice", "caliphate"]
    },
    {
      id: 2,
      type: "sermon",
      number: 2,
      title: "خطبہ بعد از بیعت",
      arabic: "الْحَمْدُ لِلَّهِ الَّذِي لَا يَبْلُغُ مِدْحَتَهُ الْقَائِلُونَ، وَلَا يُحْصِي نَعْمَاءَهُ الْعَادُّونَ، وَلَا يُؤَدِّي حَقَّهُ الْمُجْتَهِدُونَ",
      english: "Praise be to Allah whose worth cannot be described by speakers, whose bounties cannot be counted by calculators and whose claim (to obedience) cannot be satisfied by those who attempt to do so.",
      urdu: "تمام تعریفیں اللہ کے لیے ہیں جس کی تعریف بیان کرنے والے بیان نہیں کر سکتے، جس کی نعمتوں کو شمار کرنے والے شمار نہیں کر سکتے اور جس کا حق ادا کرنے والے ادا نہیں کر سکتے۔",
      category: "praise",
      tags: ["Allah", "praise", "gratitude"]
    }
  ],
  
  letters: [
    {
      id: 51,
      type: "letter",
      number: 53,
      title: "مالک اشتر کو تعلیمات",
      arabic: "هَذَا مَا أَمَرَ بِهِ عَبْدُ اللَّهِ عَلِيُّ أَمِيرُ الْمُؤْمِنِينَ، مَالِكَ بْنَ الْحَارِثِ الْأَشْتَرَ فِي عَهْدِهِ إِلَيْهِ، حِينَ وَلَّاهُ مِصْرَ",
      english: "This is what Allah's servant Ali, Commander of the Faithful, ordered Malik ibn al-Harith al-Ashtar in his instrument (of appointment) for him when he made him Governor of Egypt.",
      urdu: "یہ وہ ہدایات ہیں جو اللہ کے بندے علی امیر المومنین نے مالک بن حارث اشتر کو دی تھیں جب انہیں مصر کا گورنر مقرر کیا تھا۔",
      category: "governance",
      tags: ["governance", "justice", "administration"]
    }
  ],
  
  sayings: [
    {
      id: 101,
      type: "saying",
      number: 1,
      title: "قیمت انسان",
      arabic: "قِيمَةُ كُلِّ امْرِئٍ مَا يُحْسِنُهُ",
      english: "The worth of every man is in what he does well.",
      urdu: "ہر انسان کی قیمت اس کام میں ہے جو وہ اچھی طرح کرتا ہے۔",
      category: "wisdom",
      tags: ["value", "skill", "excellence"]
    },
    {
      id: 102,
      type: "saying",
      number: 2,
      title: "علم کی فضیلت",
      arabic: "الْعِلْمُ خَيْرُ مِنَ الْمَالِ، الْعِلْمُ يَحْرُسُكَ وَأَنْتَ تَحْرُسُ الْمَالَ",
      english: "Knowledge is better than wealth. Knowledge guards you while you have to guard wealth.",
      urdu: "علم مال سے بہتر ہے۔ علم تمہاری حفاظت کرتا ہے جبکہ تم مال کی حفاظت کرتے ہو۔",
      category: "knowledge",
      tags: ["knowledge", "wealth", "wisdom"]
    },
    {
      id: 103,
      type: "saying",
      number: 3,
      title: "صبر کی اہمیت",
      arabic: "الصَّبْرُ مِنَ الْإِيمَانِ بِمَنْزِلَةِ الرَّأْسِ مِنَ الْجَسَدِ",
      english: "Patience is to faith as the head is to the body.",
      urdu: "صبر کا ایمان کے ساتھ وہی تعلق ہے جو سر کا جسم کے ساتھ ہے۔",
      category: "character",
      tags: ["patience", "faith", "character"]
    },
    {
      id: 104,
      type: "saying",
      number: 4,
      title: "دوستی کا معیار",
      arabic: "لَا تَكُنْ مِمَّنْ لَا يَنْتَفِعُ بِالْمَوْعِظَةِ إِلَّا إِذَا بَالَغْتَ فِي إِيلَامِهِ",
      english: "Do not be among those who do not benefit from advice unless you exaggerate in hurting them.",
      urdu: "ان لوگوں میں سے نہ بنو جو نصیحت سے فائدہ نہیں اٹھاتے جب تک کہ انہیں سخت تکلیف نہ دی جائے۔",
      category: "character",
      tags: ["advice", "learning", "character"]
    },
    {
      id: 105,
      type: "saying",
      number: 5,
      title: "عدل کی اہمیت",
      arabic: "الْعَدْلُ أَسَاسُ الْمُلْكِ",
      english: "Justice is the foundation of sovereignty.",
      urdu: "عدل حکومت کی بنیاد ہے۔",
      category: "justice",
      tags: ["justice", "governance", "foundation"]
    },
    {
      id: 106,
      type: "saying",
      number: 6,
      title: "تواضع کی فضیلت",
      arabic: "مَا رَأَيْتُ نِعْمَةً مَوْفُورَةً إِلَّا وَإِلَى جَانِبِهَا حَقٌّ مُضَيَّعٌ",
      english: "I have not seen any blessing in abundance except that there is a neglected right beside it.",
      urdu: "میں نے کوئی نعمت کثرت میں نہیں دیکھی مگر اس کے ساتھ کوئی ضائع شدہ حق بھی تھا۔",
      category: "wisdom",
      tags: ["blessing", "rights", "responsibility"]
    },
    {
      id: 107,
      type: "saying",
      number: 7,
      title: "دنیا کی حقیقت",
      arabic: "الدُّنْيَا دَارُ مَمَرٍّ لَا دَارُ مَقَرٍّ",
      english: "This world is a place of passage, not a place of permanent stay.",
      urdu: "دنیا گزارنے کی جگہ ہے، ٹھہرنے کی جگہ نہیں۔",
      category: "worldly_life",
      tags: ["world", "temporary", "afterlife"]
    },
    {
      id: 108,
      type: "saying",
      number: 8,
      title: "خاموشی کی حکمت",
      arabic: "إِذَا تَمَّ الْعَقْلُ نَقَصَ الْكَلَامُ",
      english: "When intellect is complete, speech decreases.",
      urdu: "جب عقل مکمل ہو جاتی ہے تو بات کم ہو جاتی ہے۔",
      category: "wisdom",
      tags: ["intellect", "speech", "wisdom"]
    },
    {
      id: 109,
      type: "saying",
      number: 9,
      title: "غرور کا نقصان",
      arabic: "آفَةُ الْعَقْلِ الْهَوَى وَالْعُجْبُ",
      english: "The bane of intellect is desire and vanity.",
      urdu: "عقل کی آفت خواہش اور تکبر ہے۔",
      category: "character",
      tags: ["intellect", "desire", "vanity"]
    },
    {
      id: 110,
      type: "saying",
      number: 10,
      title: "محنت کا ثمر",
      arabic: "مَنْ جَدَّ وَجَدَ",
      english: "Whoever strives, finds.",
      urdu: "جو محنت کرتا ہے، وہ پاتا ہے۔",
      category: "effort",
      tags: ["effort", "success", "perseverance"]
    }
  ]
};

// Categories for filtering
const categories = {
  sermons: {
    governance: "Governance & Leadership",
    praise: "Praise & Worship",
    social: "Social Issues",
    spiritual: "Spiritual Guidance"
  },
  letters: {
    governance: "Administrative Letters",
    personal: "Personal Correspondence",
    military: "Military Instructions",
    judicial: "Judicial Matters"
  },
  sayings: {
    wisdom: "General Wisdom",
    knowledge: "Knowledge & Learning",
    character: "Character Building",
    justice: "Justice & Fairness",
    worldly_life: "Worldly Life",
    effort: "Effort & Perseverance"
  }
};

// Helper functions
const DataHelper = {
  // Get all content
  getAllContent() {
    return [
      ...nahjulBalaghData.sermons,
      ...nahjulBalaghData.letters,
      ...nahjulBalaghData.sayings
    ];
  },

  // Get content by type
  getContentByType(type) {
    return nahjulBalaghData[type] || [];
  },

  // Get content by category
  getContentByCategory(category) {
    return this.getAllContent().filter(item => item.category === category);
  },

  // Search content
  searchContent(query) {
    const searchTerm = query.toLowerCase();
    return this.getAllContent().filter(item => 
      item.arabic.toLowerCase().includes(searchTerm) ||
      item.english.toLowerCase().includes(searchTerm) ||
      item.urdu.toLowerCase().includes(searchTerm) ||
      item.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );
  },

  // Get random quote
  getRandomQuote() {
    const allContent = this.getAllContent();
    const randomIndex = Math.floor(Math.random() * allContent.length);
    return allContent[randomIndex];
  },

  // Get content by ID
  getContentById(id) {
    return this.getAllContent().find(item => item.id === id);
  },

  // Get content statistics
  getStatistics() {
    return {
      sermons: nahjulBalaghData.sermons.length,
      letters: nahjulBalaghData.letters.length,
      sayings: nahjulBalaghData.sayings.length,
      total: this.getAllContent().length
    };
  }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { nahjulBalaghData, categories, DataHelper };
}
