/* ===== RESPONSIVE DESIGN ===== */

/* Large Desktop */
@media (min-width: 1400px) {
  .container {
    max-width: 1400px;
  }
  
  .hero-title .arabic {
    font-size: 5rem;
  }
  
  .hero-title .english {
    font-size: 2.5rem;
  }
  
  .section-title {
    font-size: 3rem;
  }
}

/* Desktop */
@media (max-width: 1199px) {
  .about-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .about-stats {
    flex-direction: row;
    justify-content: center;
  }
}

/* Tablet */
@media (max-width: 991px) {
  .nav-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-tertiary);
    flex-direction: column;
    padding: var(--spacing-md);
    box-shadow: var(--shadow-lg);
    border-top: 1px solid var(--border-color);
  }
  
  .nav-menu.active {
    display: flex;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
  
  .mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }
  
  .mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
  }
  
  .mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }
  
  .hero-title .arabic {
    font-size: 3rem;
  }
  
  .hero-title .english {
    font-size: 1.5rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .quote-text .arabic {
    font-size: 1.5rem;
  }
  
  .quote-text .translation {
    font-size: 1rem;
  }
  
  .quote-actions {
    flex-wrap: wrap;
  }
}

/* Mobile Large */
@media (max-width: 767px) {
  .container {
    padding: 0 var(--spacing-sm);
  }
  
  .nav-container {
    padding: 0 var(--spacing-sm);
  }
  
  .hero {
    min-height: 80vh;
    padding: var(--spacing-xl) 0;
  }
  
  .hero-title .arabic {
    font-size: 2.5rem;
  }
  
  .hero-title .english {
    font-size: 1.2rem;
  }
  
  .hero-subtitle {
    font-size: 0.9rem;
    margin: var(--spacing-md) 0 var(--spacing-lg);
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .about,
  .categories,
  .quote-section {
    padding: var(--spacing-xl) 0;
  }
  
  .about-stats {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .stat-item {
    padding: var(--spacing-md);
  }
  
  .stat-number {
    font-size: 2rem;
  }
  
  .categories-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .category-card {
    padding: var(--spacing-lg);
  }
  
  .category-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
  
  .quote-card {
    padding: var(--spacing-lg);
  }
  
  .quote-text .arabic {
    font-size: 1.3rem;
  }
  
  .action-btn {
    width: 45px;
    height: 45px;
    font-size: 1rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    text-align: center;
  }
}

/* Mobile Small */
@media (max-width: 575px) {
  html {
    font-size: 14px;
  }
  
  .nav-brand h1 {
    font-size: 1.5rem;
  }
  
  .nav-brand span {
    font-size: 0.8rem;
  }
  
  .hero-title .arabic {
    font-size: 2rem;
  }
  
  .hero-title .english {
    font-size: 1rem;
  }
  
  .hero-subtitle {
    font-size: 0.85rem;
  }
  
  .btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 0.9rem;
  }
  
  .section-title {
    font-size: 1.8rem;
  }
  
  .quote-text .arabic {
    font-size: 1.1rem;
  }
  
  .quote-text .translation {
    font-size: 0.9rem;
  }
  
  .quote-actions {
    gap: var(--spacing-sm);
  }
  
  .action-btn {
    width: 40px;
    height: 40px;
    font-size: 0.9rem;
  }
  
  .category-card h3 {
    font-size: 1.2rem;
  }
  
  .category-card p {
    font-size: 0.9rem;
  }
}

/* Extra Small Mobile */
@media (max-width: 375px) {
  .container {
    padding: 0 var(--spacing-xs);
  }
  
  .hero-title .arabic {
    font-size: 1.8rem;
  }
  
  .quote-card {
    padding: var(--spacing-md);
  }
  
  .category-card {
    padding: var(--spacing-md);
  }
  
  .category-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
}

/* Landscape orientation adjustments */
@media (max-height: 500px) and (orientation: landscape) {
  .hero {
    min-height: 100vh;
    padding: var(--spacing-lg) 0;
  }
  
  .hero-title .arabic {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    margin: var(--spacing-sm) 0 var(--spacing-md);
  }
}

/* Print styles */
@media print {
  .navbar,
  .hero-buttons,
  .quote-actions,
  .footer {
    display: none;
  }
  
  .hero {
    min-height: auto;
    padding: var(--spacing-md) 0;
  }
  
  .quote-section {
    background: white !important;
    color: black !important;
  }
  
  .quote-card {
    background: white !important;
    border: 1px solid #ccc !important;
  }
  
  .quote-text .arabic {
    color: black !important;
  }
  
  * {
    box-shadow: none !important;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .islamic-pattern,
  .islamic-pattern-bg {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Accessibility - Large text support */
@media (min-width: 768px) {
  .large-text .hero-title .arabic {
    font-size: 4.5rem;
  }
  
  .large-text .section-title {
    font-size: 3.5rem;
  }
  
  .large-text .quote-text .arabic {
    font-size: 2.2rem;
  }
  
  .large-text .quote-text .translation {
    font-size: 1.4rem;
  }
}
