# Nahjul Balagha Digital Collection
## نہج البلاغہ - The Peak of Eloquence

A complete, responsive, and modern website for the collection of sermons, letters, and wise sayings of <PERSON> (a.s) - Nahjul Balagha.

## 🌟 Features

### 📚 Content Management
- **Complete Collection**: Sermons (خطبات), Letters (مکتوبات), and Sayings (اقوالِ زریں)
- **Multilingual Support**: Arabic text with English and Urdu translations
- **Structured Data**: JSON-based content with categories, tags, and references
- **Search & Filter**: Advanced search with multiple filters and sorting options

### 🎨 Design & User Experience
- **Islamic Design**: Elegant UI with Islamic colors and typography
- **Responsive Layout**: Optimized for mobile, tablet, and desktop
- **Dark/Light Themes**: Multiple theme options including high contrast
- **Arabic Font Support**: Beautiful Arabic typography with Amiri and Scheherazade fonts
- **Smooth Animations**: GSAP-powered animations with reduced motion support

### 🔧 Functionality
- **Quote of the Day**: Random wisdom displayed on each visit
- **Bookmark System**: Save favorite quotes locally
- **Social Sharing**: Share quotes on social media or copy to clipboard
- **Audio Recitation**: Placeholder for Arabic audio playback
- **Language Toggle**: Switch between English and Urdu interfaces
- **Keyboard Navigation**: Full keyboard accessibility support

### 📱 Modern Web Standards
- **Progressive Enhancement**: Works without JavaScript
- **Accessibility**: WCAG compliant with screen reader support
- **Performance**: Optimized loading and smooth interactions
- **SEO Friendly**: Proper meta tags and semantic HTML

## 🏗️ Project Structure

```
nahjul-balagha/
├── index.html              # Homepage with introduction
├── browse.html             # Browse all content
├── search.html             # Advanced search functionality
├── css/
│   ├── style.css          # Main stylesheet
│   ├── themes.css         # Theme system
│   └── responsive.css     # Mobile responsiveness
├── js/
│   ├── main.js           # Core functionality
│   ├── data.js           # Content data and helpers
│   ├── browse.js         # Browse page logic
│   ├── search.js         # Search functionality
│   ├── themes.js         # Theme management
│   └── animations.js     # Animation system
└── README.md             # Documentation
```

## 🚀 Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- No server required - runs entirely in the browser

### Installation
1. Download or clone the project files
2. Open `index.html` in your web browser
3. Start exploring the wisdom of Imam Ali (a.s)

### Adding Content
To add more content, edit the `js/data.js` file:

```javascript
// Add new entries to the appropriate array
nahjulBalaghData.sayings.push({
  id: 111,
  type: "saying",
  number: 11,
  title: "New Saying Title",
  arabic: "Arabic text here",
  english: "English translation here",
  urdu: "Urdu translation here",
  category: "wisdom",
  tags: ["tag1", "tag2"]
});
```

## 🎯 Key Pages

### Homepage (`index.html`)
- Introduction to Nahjul Balagha
- Quote of the Day feature
- Category overview
- Statistics display

### Browse Page (`browse.html`)
- Complete content listing
- Filter by type and category
- Grid and list view options
- Detailed content modal

### Search Page (`search.html`)
- Advanced search functionality
- Popular search suggestions
- Quick access features
- Bookmark management

## 🎨 Customization

### Themes
The website supports multiple themes defined in `css/themes.css`:
- Light Theme (default)
- Dark Theme
- Sepia Theme
- High Contrast Theme

### Colors
Islamic color palette defined in CSS variables:
```css
:root {
  --primary-gold: #D4AF37;
  --primary-green: #0F5132;
  --primary-blue: #1B4D72;
  --accent-teal: #20B2AA;
}
```

### Fonts
- Arabic: Amiri, Scheherazade New
- English: Inter
- Urdu: Amiri

## 🔧 Technical Details

### Browser Support
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### Dependencies
- Font Awesome 6.4.0 (icons)
- Google Fonts (typography)
- GSAP 3.12.2 (animations)

### Performance
- Optimized CSS with minimal reflows
- Efficient JavaScript with event delegation
- Lazy loading for better performance
- Compressed assets

## 📱 Responsive Breakpoints

- **Mobile Small**: 375px and below
- **Mobile Large**: 576px and below
- **Tablet**: 768px and below
- **Desktop**: 992px and below
- **Large Desktop**: 1200px and above

## ♿ Accessibility Features

- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode
- Reduced motion support
- Focus indicators

## 🌐 Internationalization

The website supports:
- **Arabic**: Original text (RTL)
- **English**: Primary interface language
- **Urdu**: Secondary interface language (RTL)

Language switching affects:
- Interface text
- Text direction (LTR/RTL)
- Font selection
- Layout adjustments

## 📄 License

This project is created for educational and spiritual purposes. The content of Nahjul Balagha is in the public domain.

## 🤝 Contributing

To contribute to this project:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For questions or support:
- Check the documentation
- Review the code comments
- Test in different browsers
- Ensure accessibility compliance

## 🙏 Acknowledgments

- **Imam Ali (a.s)**: For the timeless wisdom
- **Sharif Radi**: For compiling Nahjul Balagha
- **Islamic Community**: For preserving this heritage
- **Open Source Community**: For the tools and libraries used

---

**"The worth of every man is in what he does well."** - Imam Ali (a.s)

*May this digital collection help preserve and spread the wisdom of Imam Ali (a.s) for future generations.*
