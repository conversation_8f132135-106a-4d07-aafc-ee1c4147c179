// Search page functionality for Nahjul Balagha website

class SearchPage {
  constructor() {
    this.searchResults = [];
    this.currentQuery = '';
    this.searchOptions = {
      field: 'all',
      type: 'all',
      numberFrom: null,
      numberTo: null
    };
    this.bookmarks = this.loadBookmarks();

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.setupKeyboardShortcuts();
  }

  setupEventListeners() {
    // Search input and button
    const searchInput = document.getElementById('search-input');
    const searchBtn = document.getElementById('search-btn');

    searchInput.addEventListener('input', (e) => {
      this.currentQuery = e.target.value.trim();
      if (this.currentQuery.length > 2) {
        this.performSearch();
      } else if (this.currentQuery.length === 0) {
        this.showWelcomeMessage();
      }
    });

    searchInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.performSearch();
      }
    });

    searchBtn.addEventListener('click', () => {
      this.performSearch();
    });

    // Suggestion tags
    const suggestionTags = document.querySelectorAll('.suggestion-tag');
    suggestionTags.forEach(tag => {
      tag.addEventListener('click', () => {
        const query = tag.dataset.query;
        searchInput.value = query;
        this.currentQuery = query;
        this.performSearch();
      });
    });

    // Advanced search toggle
    const advancedToggle = document.getElementById('advanced-toggle');
    const advancedOptions = document.getElementById('advanced-options');

    advancedToggle.addEventListener('click', () => {
      advancedOptions.classList.toggle('hidden');
      const icon = advancedToggle.querySelector('i');
      icon.classList.toggle('fa-cog');
      icon.classList.toggle('fa-times');
    });

    // Advanced search options
    document.getElementById('search-field').addEventListener('change', (e) => {
      this.searchOptions.field = e.target.value;
      if (this.currentQuery) this.performSearch();
    });

    document.getElementById('search-type').addEventListener('change', (e) => {
      this.searchOptions.type = e.target.value;
      if (this.currentQuery) this.performSearch();
    });

    document.getElementById('number-from').addEventListener('input', (e) => {
      this.searchOptions.numberFrom = e.target.value ? parseInt(e.target.value) : null;
      if (this.currentQuery) this.performSearch();
    });

    document.getElementById('number-to').addEventListener('input', (e) => {
      this.searchOptions.numberTo = e.target.value ? parseInt(e.target.value) : null;
      if (this.currentQuery) this.performSearch();
    });

    // Quick access cards
    const quickCards = document.querySelectorAll('.quick-card');
    quickCards.forEach(card => {
      card.addEventListener('click', () => {
        const action = card.dataset.action;
        this.handleQuickAction(action);
      });
    });
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl/Cmd + K to focus search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        document.getElementById('search-input').focus();
      }

      // Escape to clear search
      if (e.key === 'Escape') {
        this.clearSearch();
      }
    });
  }

  performSearch() {
    if (!this.currentQuery) {
      this.showWelcomeMessage();
      return;
    }

    // Show loading state
    this.showLoadingState();

    // Simulate search delay for better UX
    setTimeout(() => {
      this.searchResults = this.executeSearch(this.currentQuery, this.searchOptions);
      this.displayResults();
    }, 300);
  }

  executeSearch(query, options) {
    let allContent = DataHelper.getAllContent();
    
    // Filter by type if specified
    if (options.type !== 'all') {
      allContent = allContent.filter(item => item.type === options.type);
    }

    // Filter by number range if specified
    if (options.numberFrom !== null || options.numberTo !== null) {
      allContent = allContent.filter(item => {
        const number = item.number;
        const fromMatch = options.numberFrom === null || number >= options.numberFrom;
        const toMatch = options.numberTo === null || number <= options.numberTo;
        return fromMatch && toMatch;
      });
    }

    // Perform text search
    const searchTerm = query.toLowerCase();
    const results = allContent.filter(item => {
      switch (options.field) {
        case 'arabic':
          return item.arabic.toLowerCase().includes(searchTerm);
        case 'english':
          return item.english.toLowerCase().includes(searchTerm);
        case 'urdu':
          return item.urdu.toLowerCase().includes(searchTerm);
        case 'tags':
          return item.tags.some(tag => tag.toLowerCase().includes(searchTerm));
        case 'all':
        default:
          return (
            item.arabic.toLowerCase().includes(searchTerm) ||
            item.english.toLowerCase().includes(searchTerm) ||
            item.urdu.toLowerCase().includes(searchTerm) ||
            item.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
            (item.title && item.title.toLowerCase().includes(searchTerm))
          );
      }
    });

    // Sort results by relevance (simple scoring)
    return results.sort((a, b) => {
      const scoreA = this.calculateRelevanceScore(a, searchTerm);
      const scoreB = this.calculateRelevanceScore(b, searchTerm);
      return scoreB - scoreA;
    });
  }

  calculateRelevanceScore(item, searchTerm) {
    let score = 0;
    
    // Exact matches get higher scores
    if (item.english.toLowerCase().includes(searchTerm)) score += 10;
    if (item.arabic.toLowerCase().includes(searchTerm)) score += 8;
    if (item.urdu.toLowerCase().includes(searchTerm)) score += 8;
    
    // Tag matches
    item.tags.forEach(tag => {
      if (tag.toLowerCase().includes(searchTerm)) score += 5;
    });
    
    // Title matches (if exists)
    if (item.title && item.title.toLowerCase().includes(searchTerm)) score += 15;
    
    return score;
  }

  displayResults() {
    const resultsContainer = document.getElementById('search-results');
    
    if (this.searchResults.length === 0) {
      this.showNoResults();
      return;
    }

    const resultsHTML = `
      <div class="results-header">
        <h3>
          <span class="english">Search Results (${this.searchResults.length})</span>
          <span class="urdu hidden">تلاش کے نتائج (${this.searchResults.length})</span>
        </h3>
        <button id="clear-search" class="btn btn-secondary">
          <i class="fas fa-times"></i>
          <span class="english">Clear</span>
          <span class="urdu hidden">صاف کریں</span>
        </button>
      </div>
      <div class="results-grid">
        ${this.searchResults.map(item => this.createResultCard(item)).join('')}
      </div>
    `;

    resultsContainer.innerHTML = resultsHTML;

    // Add event listeners to result cards
    this.setupResultCardListeners();

    // Add clear search listener
    document.getElementById('clear-search').addEventListener('click', () => {
      this.clearSearch();
    });

    // Animate results
    this.animateResults();
  }

  createResultCard(item) {
    const typeIcon = this.getTypeIcon(item.type);
    const isBookmarked = this.bookmarks.includes(item.id);
    
    return `
      <div class="result-card" data-id="${item.id}">
        <div class="result-header">
          <div class="result-type">
            <i class="${typeIcon}"></i>
            <span>${this.formatTypeName(item.type)} #${item.number}</span>
          </div>
          <button class="bookmark-btn ${isBookmarked ? 'active' : ''}" data-id="${item.id}">
            <i class="${isBookmarked ? 'fas' : 'far'} fa-bookmark"></i>
          </button>
        </div>
        
        <div class="result-content">
          <div class="arabic-text">${this.highlightSearchTerm(item.arabic, this.currentQuery)}</div>
          <div class="translation-text">
            <p class="english">${this.highlightSearchTerm(item.english, this.currentQuery)}</p>
            <p class="urdu hidden">${this.highlightSearchTerm(item.urdu, this.currentQuery)}</p>
          </div>
        </div>
        
        <div class="result-footer">
          <div class="result-tags">
            ${item.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
          </div>
          <div class="result-actions">
            <button class="action-btn share-btn" data-id="${item.id}" title="Share">
              <i class="fas fa-share-alt"></i>
            </button>
            <button class="action-btn copy-btn" data-id="${item.id}" title="Copy">
              <i class="fas fa-copy"></i>
            </button>
          </div>
        </div>
      </div>
    `;
  }

  highlightSearchTerm(text, searchTerm) {
    if (!searchTerm || searchTerm.length < 2) return text;
    
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }

  setupResultCardListeners() {
    // Result card clicks
    const resultCards = document.querySelectorAll('.result-card');
    resultCards.forEach(card => {
      card.addEventListener('click', (e) => {
        if (!e.target.closest('.bookmark-btn, .action-btn')) {
          const itemId = parseInt(card.dataset.id);
          const item = DataHelper.getContentById(itemId);
          if (item) {
            this.openContentModal(item);
          }
        }
      });
    });

    // Bookmark buttons
    const bookmarkBtns = document.querySelectorAll('.bookmark-btn');
    bookmarkBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const itemId = parseInt(btn.dataset.id);
        this.toggleBookmark(itemId);
      });
    });

    // Action buttons
    const shareBtns = document.querySelectorAll('.share-btn');
    const copyBtns = document.querySelectorAll('.copy-btn');

    shareBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const itemId = parseInt(btn.dataset.id);
        const item = DataHelper.getContentById(itemId);
        if (item) this.shareContent(item);
      });
    });

    copyBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const itemId = parseInt(btn.dataset.id);
        const item = DataHelper.getContentById(itemId);
        if (item) this.copyContent(item);
      });
    });
  }

  openContentModal(item) {
    // Create a simple modal or redirect to browse page with the item
    const url = `browse.html#item-${item.id}`;
    window.location.href = url;
  }

  toggleBookmark(itemId) {
    const isBookmarked = this.bookmarks.includes(itemId);
    
    if (isBookmarked) {
      this.bookmarks = this.bookmarks.filter(id => id !== itemId);
    } else {
      this.bookmarks.push(itemId);
    }
    
    this.saveBookmarks();
    this.updateBookmarkButton(itemId, !isBookmarked);
    this.showNotification(isBookmarked ? 'Bookmark removed' : 'Content bookmarked');
  }

  updateBookmarkButton(itemId, isBookmarked) {
    const bookmarkBtn = document.querySelector(`.bookmark-btn[data-id="${itemId}"]`);
    if (bookmarkBtn) {
      const icon = bookmarkBtn.querySelector('i');
      icon.className = isBookmarked ? 'fas fa-bookmark' : 'far fa-bookmark';
      bookmarkBtn.classList.toggle('active', isBookmarked);
    }
  }

  shareContent(item) {
    const shareText = `"${item.english}"\n\n- Imam Ali (a.s)\nNahjul Balagha, ${this.formatTypeName(item.type)} #${item.number}`;
    
    if (navigator.share) {
      navigator.share({
        title: `Nahjul Balagha - ${this.formatTypeName(item.type)} #${item.number}`,
        text: shareText,
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(shareText).then(() => {
        this.showNotification('Content copied to clipboard');
      });
    }
  }

  copyContent(item) {
    const copyText = `${item.arabic}\n\n${item.english}\n\n${item.urdu}\n\n- Imam Ali (a.s), Nahjul Balagha ${this.formatTypeName(item.type)} #${item.number}`;
    
    navigator.clipboard.writeText(copyText).then(() => {
      this.showNotification('Content copied to clipboard');
    });
  }

  handleQuickAction(action) {
    switch (action) {
      case 'random':
        const randomItem = DataHelper.getRandomQuote();
        this.openContentModal(randomItem);
        break;
      case 'bookmarks':
        this.showBookmarks();
        break;
      case 'browse-all':
        window.location.href = 'browse.html';
        break;
      case 'daily-quote':
        window.location.href = 'index.html#quote-of-day';
        break;
    }
  }

  showBookmarks() {
    if (this.bookmarks.length === 0) {
      this.showNotification('No bookmarks found');
      return;
    }

    const bookmarkedItems = this.bookmarks.map(id => DataHelper.getContentById(id)).filter(Boolean);
    this.searchResults = bookmarkedItems;
    this.currentQuery = 'bookmarks';
    
    const resultsContainer = document.getElementById('search-results');
    const resultsHTML = `
      <div class="results-header">
        <h3>
          <span class="english">My Bookmarks (${bookmarkedItems.length})</span>
          <span class="urdu hidden">میرے بک مارکس (${bookmarkedItems.length})</span>
        </h3>
        <button id="clear-search" class="btn btn-secondary">
          <i class="fas fa-times"></i>
          <span class="english">Clear</span>
          <span class="urdu hidden">صاف کریں</span>
        </button>
      </div>
      <div class="results-grid">
        ${bookmarkedItems.map(item => this.createResultCard(item)).join('')}
      </div>
    `;

    resultsContainer.innerHTML = resultsHTML;
    this.setupResultCardListeners();
    
    document.getElementById('clear-search').addEventListener('click', () => {
      this.clearSearch();
    });
  }

  showWelcomeMessage() {
    const resultsContainer = document.getElementById('search-results');
    resultsContainer.innerHTML = `
      <div class="welcome-message">
        <i class="fas fa-search"></i>
        <h3>
          <span class="english">Start your search</span>
          <span class="urdu hidden">اپنی تلاش شروع کریں</span>
        </h3>
        <p>
          <span class="english">Enter keywords to find relevant content from Nahjul Balagha</span>
          <span class="urdu hidden">نہج البلاغہ سے متعلقہ مواد تلاش کرنے کے لیے کلیدی الفاظ درج کریں</span>
        </p>
      </div>
    `;
  }

  showLoadingState() {
    const resultsContainer = document.getElementById('search-results');
    resultsContainer.innerHTML = `
      <div class="loading-state">
        <div class="loading-spinner"></div>
        <p>
          <span class="english">Searching...</span>
          <span class="urdu hidden">تلاش کر رہے ہیں...</span>
        </p>
      </div>
    `;
  }

  showNoResults() {
    const resultsContainer = document.getElementById('search-results');
    resultsContainer.innerHTML = `
      <div class="no-results">
        <i class="fas fa-search"></i>
        <h3>
          <span class="english">No results found</span>
          <span class="urdu hidden">کوئی نتیجہ نہیں ملا</span>
        </h3>
        <p>
          <span class="english">Try different keywords or adjust your search filters</span>
          <span class="urdu hidden">مختلف کلیدی الفاظ آزمائیں یا اپنے سرچ فلٹرز کو تبدیل کریں</span>
        </p>
        <button id="clear-search" class="btn btn-primary">
          <i class="fas fa-refresh"></i>
          <span class="english">Clear Search</span>
          <span class="urdu hidden">سرچ صاف کریں</span>
        </button>
      </div>
    `;

    document.getElementById('clear-search').addEventListener('click', () => {
      this.clearSearch();
    });
  }

  clearSearch() {
    document.getElementById('search-input').value = '';
    this.currentQuery = '';
    this.searchResults = [];
    this.showWelcomeMessage();
    
    // Reset advanced options
    document.getElementById('search-field').value = 'all';
    document.getElementById('search-type').value = 'all';
    document.getElementById('number-from').value = '';
    document.getElementById('number-to').value = '';
    
    this.searchOptions = {
      field: 'all',
      type: 'all',
      numberFrom: null,
      numberTo: null
    };
  }

  animateResults() {
    if (window.getAnimationManager && !window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      const animationManager = window.getAnimationManager();
      if (animationManager && animationManager.isGSAPLoaded) {
        gsap.from('.result-card', {
          duration: 0.5,
          y: 30,
          opacity: 0,
          stagger: 0.1,
          ease: 'power2.out'
        });
      }
    }
  }

  getTypeIcon(type) {
    const icons = {
      sermon: 'fas fa-microphone',
      letter: 'fas fa-envelope',
      saying: 'fas fa-quote-right'
    };
    return icons[type] || 'fas fa-book';
  }

  formatTypeName(type) {
    const names = {
      sermon: 'Sermon',
      letter: 'Letter',
      saying: 'Saying'
    };
    return names[type] || type;
  }

  loadBookmarks() {
    const saved = localStorage.getItem('nahjul-balagha-bookmarks');
    return saved ? JSON.parse(saved) : [];
  }

  saveBookmarks() {
    localStorage.setItem('nahjul-balagha-bookmarks', JSON.stringify(this.bookmarks));
  }

  showNotification(message) {
    // Reuse notification system from main.js
    if (window.nahjulBalaghApp) {
      window.nahjulBalaghApp.showNotification(message);
    }
  }
}

// Initialize search page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.searchPage = new SearchPage();
});
