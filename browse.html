<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browse Content - Nahjul Balagha | نہج البلاغہ</title>
    <meta name="description" content="Browse all sermons, letters, and sayings from Nahjul Balagha">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&family=Scheherazade+New:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/themes.css">
    <link rel="stylesheet" href="css/responsive.css">
    
    <!-- GSAP -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
</head>
<body class="light-theme">
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h1>نہج البلاغہ</h1>
                <span>Nahjul Balagha</span>
            </div>
            
            <div class="nav-menu">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </a>
                <a href="browse.html" class="nav-link active">
                    <i class="fas fa-book-open"></i>
                    <span>Browse</span>
                </a>
                <a href="search.html" class="nav-link">
                    <i class="fas fa-search"></i>
                    <span>Search</span>
                </a>
                <div class="nav-controls">
                    <button id="theme-toggle" class="control-btn">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button id="language-toggle" class="control-btn">
                        <span>اردو</span>
                    </button>
                </div>
            </div>
            
            <div class="mobile-menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1 class="page-title">
                <span class="english">Browse Content</span>
                <span class="urdu hidden">مطالعہ کریں</span>
            </h1>
            <p class="page-subtitle">
                <span class="english">Explore the complete collection of Imam Ali's (a.s) wisdom</span>
                <span class="urdu hidden">امام علی علیہ السلام کی مکمل حکمت کا مطالعہ کریں</span>
            </p>
        </div>
    </section>

    <!-- Filters Section -->
    <section class="filters-section">
        <div class="container">
            <div class="filters-container">
                <div class="filter-group">
                    <label for="type-filter" class="filter-label">
                        <span class="english">Type:</span>
                        <span class="urdu hidden">قسم:</span>
                    </label>
                    <select id="type-filter" class="filter-select">
                        <option value="all">
                            <span class="english">All Types</span>
                            <span class="urdu hidden">تمام اقسام</span>
                        </option>
                        <option value="sermons">
                            <span class="english">Sermons</span>
                            <span class="urdu hidden">خطبات</span>
                        </option>
                        <option value="letters">
                            <span class="english">Letters</span>
                            <span class="urdu hidden">خطوط</span>
                        </option>
                        <option value="sayings">
                            <span class="english">Sayings</span>
                            <span class="urdu hidden">اقوال</span>
                        </option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="category-filter" class="filter-label">
                        <span class="english">Category:</span>
                        <span class="urdu hidden">موضوع:</span>
                    </label>
                    <select id="category-filter" class="filter-select">
                        <option value="all">
                            <span class="english">All Categories</span>
                            <span class="urdu hidden">تمام موضوعات</span>
                        </option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="sort-filter" class="filter-label">
                        <span class="english">Sort by:</span>
                        <span class="urdu hidden">ترتیب:</span>
                    </label>
                    <select id="sort-filter" class="filter-select">
                        <option value="number">
                            <span class="english">Number</span>
                            <span class="urdu hidden">نمبر</span>
                        </option>
                        <option value="type">
                            <span class="english">Type</span>
                            <span class="urdu hidden">قسم</span>
                        </option>
                        <option value="category">
                            <span class="english">Category</span>
                            <span class="urdu hidden">موضوع</span>
                        </option>
                    </select>
                </div>

                <div class="filter-actions">
                    <button id="clear-filters" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        <span class="english">Clear</span>
                        <span class="urdu hidden">صاف کریں</span>
                    </button>
                    <button id="random-content" class="btn btn-primary">
                        <i class="fas fa-random"></i>
                        <span class="english">Random</span>
                        <span class="urdu hidden">بے ترتیب</span>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Content Grid -->
    <section class="content-section">
        <div class="container">
            <div class="content-stats">
                <span id="content-count" class="stats-text">
                    <span class="english">Showing 0 items</span>
                    <span class="urdu hidden">0 آئٹمز دکھائے جا رہے ہیں</span>
                </span>
                <div class="view-toggle">
                    <button id="grid-view" class="view-btn active" title="Grid View">
                        <i class="fas fa-th"></i>
                    </button>
                    <button id="list-view" class="view-btn" title="List View">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>

            <div id="content-grid" class="content-grid">
                <!-- Content items will be dynamically loaded here -->
            </div>

            <div id="loading-more" class="loading-more hidden">
                <div class="loading-spinner"></div>
                <span class="english">Loading more content...</span>
                <span class="urdu hidden">مزید مواد لوڈ ہو رہا ہے...</span>
            </div>

            <div id="no-results" class="no-results hidden">
                <i class="fas fa-search"></i>
                <h3>
                    <span class="english">No content found</span>
                    <span class="urdu hidden">کوئی مواد نہیں ملا</span>
                </h3>
                <p>
                    <span class="english">Try adjusting your filters or search terms</span>
                    <span class="urdu hidden">اپنے فلٹرز یا تلاش کی شرائط کو تبدیل کرنے کی کوشش کریں</span>
                </p>
            </div>
        </div>
    </section>

    <!-- Content Modal -->
    <div id="content-modal" class="modal hidden">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title"></h2>
                <button id="modal-close" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="content-text">
                    <div class="arabic-text" id="modal-arabic"></div>
                    <div class="translation-text">
                        <p class="english" id="modal-english"></p>
                        <p class="urdu hidden" id="modal-urdu"></p>
                    </div>
                </div>
                <div class="content-meta">
                    <span id="modal-reference" class="reference"></span>
                    <span id="modal-category" class="category-tag"></span>
                </div>
            </div>
            <div class="modal-actions">
                <button id="modal-bookmark" class="action-btn">
                    <i class="far fa-bookmark"></i>
                    <span class="english">Bookmark</span>
                    <span class="urdu hidden">بک مارک</span>
                </button>
                <button id="modal-share" class="action-btn">
                    <i class="fas fa-share-alt"></i>
                    <span class="english">Share</span>
                    <span class="urdu hidden">شیئر</span>
                </button>
                <button id="modal-audio" class="action-btn">
                    <i class="fas fa-play"></i>
                    <span class="english">Audio</span>
                    <span class="urdu hidden">آڈیو</span>
                </button>
                <button id="modal-copy" class="action-btn">
                    <i class="fas fa-copy"></i>
                    <span class="english">Copy</span>
                    <span class="urdu hidden">کاپی</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>نہج البلاغہ</h3>
                    <p class="english">Preserving the wisdom of Imam Ali (a.s) for future generations</p>
                    <p class="urdu hidden">امام علی علیہ السلام کی حکمت کو آئندہ نسلوں کے لیے محفوظ کرنا</p>
                </div>
                <div class="footer-section">
                    <h4>
                        <span class="english">Quick Links</span>
                        <span class="urdu hidden">فوری لنکس</span>
                    </h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="browse.html">Browse All</a></li>
                        <li><a href="search.html">Search</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>
                        <span class="english">Features</span>
                        <span class="urdu hidden">خصوصیات</span>
                    </h4>
                    <ul>
                        <li>Bookmarks</li>
                        <li>Audio Recitation</li>
                        <li>Daily Reminders</li>
                        <li>Share Quotes</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Nahjul Balagha Digital Collection. Made with ❤️ for the Ummah</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/data.js"></script>
    <script src="js/themes.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/main.js"></script>
    <script src="js/browse.js"></script>
</body>
</html>
