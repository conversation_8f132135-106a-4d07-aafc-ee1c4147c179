// Animation system for Nahjul Balagha website

class AnimationManager {
  constructor() {
    this.isGSAPLoaded = typeof gsap !== 'undefined';
    this.reducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    this.init();
  }

  init() {
    if (this.reducedMotion) {
      this.disableAnimations();
      return;
    }

    this.setupScrollAnimations();
    this.setupHoverAnimations();
    this.setupLoadingAnimations();
    
    if (this.isGSAPLoaded) {
      this.setupGSAPAnimations();
    } else {
      this.setupCSSAnimations();
    }
  }

  disableAnimations() {
    const style = document.createElement('style');
    style.textContent = `
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }
    `;
    document.head.appendChild(style);
  }

  setupScrollAnimations() {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, observerOptions);

    // Observe elements for scroll animations
    const animateElements = document.querySelectorAll(
      '.stat-item, .category-card, .quote-card, .section-title'
    );
    
    animateElements.forEach(el => {
      el.classList.add('animate-on-scroll');
      observer.observe(el);
    });
  }

  setupHoverAnimations() {
    // Category cards hover effect
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach(card => {
      card.addEventListener('mouseenter', () => {
        if (!this.reducedMotion) {
          this.animateCardHover(card, true);
        }
      });
      
      card.addEventListener('mouseleave', () => {
        if (!this.reducedMotion) {
          this.animateCardHover(card, false);
        }
      });
    });

    // Button hover effects
    const buttons = document.querySelectorAll('.btn, .action-btn, .control-btn');
    buttons.forEach(btn => {
      btn.addEventListener('mouseenter', () => {
        if (!this.reducedMotion) {
          this.animateButtonHover(btn, true);
        }
      });
      
      btn.addEventListener('mouseleave', () => {
        if (!this.reducedMotion) {
          this.animateButtonHover(btn, false);
        }
      });
    });
  }

  setupLoadingAnimations() {
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
      this.animateLoadingScreen();
    }
  }

  setupGSAPAnimations() {
    // Register ScrollTrigger plugin if available
    if (typeof ScrollTrigger !== 'undefined') {
      gsap.registerPlugin(ScrollTrigger);
    }

    // Hero section entrance
    const heroTimeline = gsap.timeline({ delay: 2 });
    
    heroTimeline
      .from('.hero-title .arabic', {
        duration: 1.2,
        y: 100,
        opacity: 0,
        ease: 'power3.out'
      })
      .from('.hero-title .english', {
        duration: 1,
        y: 50,
        opacity: 0,
        ease: 'power2.out'
      }, '-=0.5')
      .from('.hero-subtitle', {
        duration: 1,
        y: 30,
        opacity: 0,
        ease: 'power2.out'
      }, '-=0.3')
      .from('.hero-buttons .btn', {
        duration: 0.8,
        y: 30,
        opacity: 0,
        stagger: 0.2,
        ease: 'power2.out'
      }, '-=0.2');

    // Scroll-triggered animations
    if (typeof ScrollTrigger !== 'undefined') {
      // About section stats
      gsap.from('.stat-item', {
        duration: 0.8,
        y: 50,
        opacity: 0,
        stagger: 0.2,
        ease: 'back.out(1.7)',
        scrollTrigger: {
          trigger: '.about-stats',
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse'
        }
      });

      // Category cards
      gsap.from('.category-card', {
        duration: 1,
        y: 80,
        opacity: 0,
        stagger: 0.3,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: '.categories-grid',
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse'
        }
      });

      // Quote section
      gsap.from('.quote-card', {
        duration: 1.2,
        scale: 0.8,
        opacity: 0,
        ease: 'elastic.out(1, 0.5)',
        scrollTrigger: {
          trigger: '.quote-section',
          start: 'top 70%',
          end: 'bottom 30%',
          toggleActions: 'play none none reverse'
        }
      });
    }

    // Floating animation for Islamic patterns
    gsap.to('.islamic-pattern-bg', {
      duration: 20,
      rotation: 360,
      ease: 'none',
      repeat: -1
    });

    // Parallax effect for hero background
    gsap.to('.hero-background', {
      yPercent: -50,
      ease: 'none',
      scrollTrigger: {
        trigger: '.hero',
        start: 'top bottom',
        end: 'bottom top',
        scrub: true
      }
    });
  }

  setupCSSAnimations() {
    // Fallback CSS animations when GSAP is not available
    const style = document.createElement('style');
    style.textContent = `
      .animate-on-scroll {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease;
      }
      
      .animate-on-scroll.animate-in {
        opacity: 1;
        transform: translateY(0);
      }
      
      .hero-title {
        animation: heroTitleFade 1.5s ease 2s both;
      }
      
      .hero-subtitle {
        animation: heroSubtitleFade 1s ease 2.5s both;
      }
      
      .hero-buttons {
        animation: heroButtonsFade 1s ease 3s both;
      }
      
      @keyframes heroTitleFade {
        from {
          opacity: 0;
          transform: translateY(50px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      @keyframes heroSubtitleFade {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      @keyframes heroButtonsFade {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      .islamic-pattern-bg {
        animation: floatPattern 20s linear infinite;
      }
      
      @keyframes floatPattern {
        0% { transform: rotate(0deg) translateY(0px); }
        25% { transform: rotate(90deg) translateY(-10px); }
        50% { transform: rotate(180deg) translateY(0px); }
        75% { transform: rotate(270deg) translateY(-10px); }
        100% { transform: rotate(360deg) translateY(0px); }
      }
    `;
    document.head.appendChild(style);
  }

  animateCardHover(card, isHover) {
    if (this.isGSAPLoaded) {
      gsap.to(card, {
        duration: 0.3,
        y: isHover ? -10 : 0,
        scale: isHover ? 1.02 : 1,
        ease: 'power2.out'
      });
    } else {
      card.style.transform = isHover ? 'translateY(-10px) scale(1.02)' : 'translateY(0) scale(1)';
    }
  }

  animateButtonHover(button, isHover) {
    if (this.isGSAPLoaded) {
      gsap.to(button, {
        duration: 0.2,
        scale: isHover ? 1.05 : 1,
        ease: 'power2.out'
      });
    } else {
      button.style.transform = isHover ? 'scale(1.05)' : 'scale(1)';
    }
  }

  animateLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    if (!loadingScreen) return;

    if (this.isGSAPLoaded) {
      gsap.to(loadingScreen, {
        duration: 0.8,
        opacity: 0,
        ease: 'power2.inOut',
        delay: 1.5,
        onComplete: () => {
          loadingScreen.style.display = 'none';
        }
      });
    } else {
      setTimeout(() => {
        loadingScreen.style.transition = 'opacity 0.8s ease';
        loadingScreen.style.opacity = '0';
        setTimeout(() => {
          loadingScreen.style.display = 'none';
        }, 800);
      }, 1500);
    }
  }

  // Quote change animation
  animateQuoteChange() {
    const quoteCard = document.querySelector('.quote-card');
    if (!quoteCard) return;

    if (this.isGSAPLoaded) {
      const tl = gsap.timeline();
      tl.to(quoteCard, {
        duration: 0.3,
        scale: 0.95,
        opacity: 0.7,
        ease: 'power2.inOut'
      })
      .to(quoteCard, {
        duration: 0.4,
        scale: 1,
        opacity: 1,
        ease: 'back.out(1.7)'
      });
    } else {
      quoteCard.style.transition = 'all 0.3s ease';
      quoteCard.style.transform = 'scale(0.95)';
      quoteCard.style.opacity = '0.7';
      
      setTimeout(() => {
        quoteCard.style.transform = 'scale(1)';
        quoteCard.style.opacity = '1';
      }, 300);
    }
  }

  // Page transition animation
  animatePageTransition(callback) {
    const overlay = document.createElement('div');
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, var(--primary-blue), var(--deep-navy));
      z-index: 9999;
      opacity: 0;
      pointer-events: none;
    `;
    
    document.body.appendChild(overlay);

    if (this.isGSAPLoaded) {
      gsap.to(overlay, {
        duration: 0.5,
        opacity: 1,
        ease: 'power2.inOut',
        onComplete: () => {
          if (callback) callback();
          gsap.to(overlay, {
            duration: 0.5,
            opacity: 0,
            ease: 'power2.inOut',
            onComplete: () => {
              document.body.removeChild(overlay);
            }
          });
        }
      });
    } else {
      overlay.style.transition = 'opacity 0.5s ease';
      overlay.style.opacity = '1';
      
      setTimeout(() => {
        if (callback) callback();
        overlay.style.opacity = '0';
        setTimeout(() => {
          document.body.removeChild(overlay);
        }, 500);
      }, 500);
    }
  }
}

// Initialize animation manager
let animationManager;

document.addEventListener('DOMContentLoaded', () => {
  animationManager = new AnimationManager();
});

// Export for global access
window.AnimationManager = AnimationManager;
window.getAnimationManager = () => animationManager;
