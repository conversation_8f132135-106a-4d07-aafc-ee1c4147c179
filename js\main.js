// Main JavaScript functionality for Nahjul Balagha website

class NahjulBalaghApp {
  constructor() {
    this.currentLanguage = 'english';
    this.currentTheme = 'light';
    this.bookmarks = this.loadBookmarks();
    this.dailyQuote = null;
    
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.loadDailyQuote();
    this.hideLoadingScreen();
    this.initializeAnimations();
  }

  setupEventListeners() {
    // Theme toggle
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
      themeToggle.addEventListener('click', () => this.toggleTheme());
    }

    // Language toggle
    const languageToggle = document.getElementById('language-toggle');
    if (languageToggle) {
      languageToggle.addEventListener('click', () => this.toggleLanguage());
    }

    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');
    if (mobileMenuToggle && navMenu) {
      mobileMenuToggle.addEventListener('click', () => {
        navMenu.classList.toggle('active');
        mobileMenuToggle.classList.toggle('active');
      });
    }

    // Quote actions
    this.setupQuoteActions();

    // Category cards
    this.setupCategoryCards();

    // Smooth scrolling for anchor links
    this.setupSmoothScrolling();

    // Keyboard navigation
    this.setupKeyboardNavigation();
  }

  setupQuoteActions() {
    const bookmarkBtn = document.getElementById('bookmark-quote');
    const shareBtn = document.getElementById('share-quote');
    const playBtn = document.getElementById('play-audio');
    const refreshBtn = document.getElementById('refresh-quote');

    if (bookmarkBtn) {
      bookmarkBtn.addEventListener('click', () => this.toggleBookmark());
    }

    if (shareBtn) {
      shareBtn.addEventListener('click', () => this.shareQuote());
    }

    if (playBtn) {
      playBtn.addEventListener('click', () => this.playAudio());
    }

    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.loadDailyQuote());
    }
  }

  setupCategoryCards() {
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach(card => {
      card.addEventListener('click', () => {
        const category = card.dataset.category;
        this.navigateToCategory(category);
      });
    });
  }

  setupSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  }

  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // Alt + T for theme toggle
      if (e.altKey && e.key === 't') {
        e.preventDefault();
        this.toggleTheme();
      }
      
      // Alt + L for language toggle
      if (e.altKey && e.key === 'l') {
        e.preventDefault();
        this.toggleLanguage();
      }
      
      // Alt + R for refresh quote
      if (e.altKey && e.key === 'r') {
        e.preventDefault();
        this.loadDailyQuote();
      }
    });
  }

  toggleTheme() {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    document.body.className = `${this.currentTheme}-theme`;
    
    // Save preference
    localStorage.setItem('nahjul-balagha-theme', this.currentTheme);
    
    // Update theme toggle icon
    const themeIcon = document.querySelector('#theme-toggle i');
    if (themeIcon) {
      themeIcon.className = this.currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
    }
  }

  toggleLanguage() {
    this.currentLanguage = this.currentLanguage === 'english' ? 'urdu' : 'english';
    
    // Toggle visibility of language elements
    const englishElements = document.querySelectorAll('.english');
    const urduElements = document.querySelectorAll('.urdu');
    
    if (this.currentLanguage === 'urdu') {
      englishElements.forEach(el => el.classList.add('hidden'));
      urduElements.forEach(el => el.classList.remove('hidden'));
      document.body.setAttribute('dir', 'rtl');
    } else {
      englishElements.forEach(el => el.classList.remove('hidden'));
      urduElements.forEach(el => el.classList.add('hidden'));
      document.body.setAttribute('dir', 'ltr');
    }
    
    // Update language toggle button
    const languageBtn = document.getElementById('language-toggle');
    if (languageBtn) {
      languageBtn.innerHTML = `<span>${this.currentLanguage === 'english' ? 'اردو' : 'English'}</span>`;
    }
    
    // Save preference
    localStorage.setItem('nahjul-balagha-language', this.currentLanguage);
  }

  loadDailyQuote() {
    // Get a random quote for "daily quote"
    this.dailyQuote = DataHelper.getRandomQuote();
    
    if (this.dailyQuote) {
      this.displayQuote(this.dailyQuote);
      this.updateBookmarkButton();
    }
  }

  displayQuote(quote) {
    const arabicEl = document.getElementById('daily-quote-arabic');
    const englishEl = document.getElementById('daily-quote-english');
    const urduEl = document.getElementById('daily-quote-urdu');
    const referenceEl = document.getElementById('daily-quote-reference');

    if (arabicEl) arabicEl.textContent = quote.arabic;
    if (englishEl) englishEl.textContent = quote.english;
    if (urduEl) urduEl.textContent = quote.urdu;
    if (referenceEl) {
      const typeMap = {
        sermon: 'Sermon',
        letter: 'Letter',
        saying: 'Saying'
      };
      referenceEl.textContent = `${typeMap[quote.type]} #${quote.number}`;
    }

    // Animate quote appearance
    this.animateQuoteChange();
  }

  animateQuoteChange() {
    const quoteCard = document.querySelector('.quote-card');
    if (quoteCard) {
      quoteCard.style.transform = 'scale(0.95)';
      quoteCard.style.opacity = '0.7';
      
      setTimeout(() => {
        quoteCard.style.transform = 'scale(1)';
        quoteCard.style.opacity = '1';
      }, 200);
    }
  }

  toggleBookmark() {
    if (!this.dailyQuote) return;
    
    const quoteId = this.dailyQuote.id;
    const isBookmarked = this.bookmarks.includes(quoteId);
    
    if (isBookmarked) {
      this.bookmarks = this.bookmarks.filter(id => id !== quoteId);
    } else {
      this.bookmarks.push(quoteId);
    }
    
    this.saveBookmarks();
    this.updateBookmarkButton();
    this.showNotification(isBookmarked ? 'Bookmark removed' : 'Quote bookmarked');
  }

  updateBookmarkButton() {
    const bookmarkBtn = document.getElementById('bookmark-quote');
    if (bookmarkBtn && this.dailyQuote) {
      const isBookmarked = this.bookmarks.includes(this.dailyQuote.id);
      const icon = bookmarkBtn.querySelector('i');
      if (icon) {
        icon.className = isBookmarked ? 'fas fa-bookmark' : 'far fa-bookmark';
      }
      bookmarkBtn.classList.toggle('active', isBookmarked);
    }
  }

  shareQuote() {
    if (!this.dailyQuote) return;
    
    const shareText = `"${this.dailyQuote.english}"\n\n- Imam Ali (a.s)\nNahjul Balagha, ${this.dailyQuote.type} #${this.dailyQuote.number}`;
    
    if (navigator.share) {
      navigator.share({
        title: 'Nahjul Balagha Quote',
        text: shareText,
        url: window.location.href
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(shareText).then(() => {
        this.showNotification('Quote copied to clipboard');
      });
    }
  }

  playAudio() {
    // Placeholder for audio functionality
    const playBtn = document.getElementById('play-audio');
    if (playBtn) {
      const icon = playBtn.querySelector('i');
      if (icon.classList.contains('fa-play')) {
        icon.className = 'fas fa-pause';
        this.showNotification('Audio playback started (demo)');
        
        // Simulate audio duration
        setTimeout(() => {
          icon.className = 'fas fa-play';
        }, 3000);
      } else {
        icon.className = 'fas fa-play';
        this.showNotification('Audio playback stopped');
      }
    }
  }

  navigateToCategory(category) {
    // Store selected category and navigate to browse page
    localStorage.setItem('nahjul-balagha-selected-category', category);
    window.location.href = 'browse.html';
  }

  showNotification(message) {
    // Create and show notification
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: var(--primary-gold);
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      z-index: 10000;
      transform: translateX(100%);
      transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 3000);
  }

  loadBookmarks() {
    const saved = localStorage.getItem('nahjul-balagha-bookmarks');
    return saved ? JSON.parse(saved) : [];
  }

  saveBookmarks() {
    localStorage.setItem('nahjul-balagha-bookmarks', JSON.stringify(this.bookmarks));
  }

  hideLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
      setTimeout(() => {
        loadingScreen.style.opacity = '0';
        setTimeout(() => {
          loadingScreen.style.display = 'none';
        }, 500);
      }, 1500);
    }
  }

  initializeAnimations() {
    // Initialize GSAP animations if available
    if (typeof gsap !== 'undefined') {
      this.setupGSAPAnimations();
    }
  }

  setupGSAPAnimations() {
    // Hero section animation
    gsap.from('.hero-title', {
      duration: 1,
      y: 50,
      opacity: 0,
      ease: 'power2.out',
      delay: 2
    });

    gsap.from('.hero-subtitle', {
      duration: 1,
      y: 30,
      opacity: 0,
      ease: 'power2.out',
      delay: 2.3
    });

    gsap.from('.hero-buttons', {
      duration: 1,
      y: 30,
      opacity: 0,
      ease: 'power2.out',
      delay: 2.6
    });

    // Scroll-triggered animations
    gsap.registerPlugin(ScrollTrigger);

    gsap.from('.stat-item', {
      duration: 0.8,
      y: 50,
      opacity: 0,
      stagger: 0.2,
      ease: 'power2.out',
      scrollTrigger: {
        trigger: '.about-stats',
        start: 'top 80%'
      }
    });

    gsap.from('.category-card', {
      duration: 0.8,
      y: 50,
      opacity: 0,
      stagger: 0.2,
      ease: 'power2.out',
      scrollTrigger: {
        trigger: '.categories-grid',
        start: 'top 80%'
      }
    });
  }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Load saved preferences
  const savedTheme = localStorage.getItem('nahjul-balagha-theme') || 'light';
  const savedLanguage = localStorage.getItem('nahjul-balagha-language') || 'english';
  
  document.body.className = `${savedTheme}-theme`;
  
  // Initialize app
  window.nahjulBalaghApp = new NahjulBalaghApp();
  
  // Apply saved language
  if (savedLanguage === 'urdu') {
    window.nahjulBalaghApp.toggleLanguage();
  }
  
  // Apply saved theme
  if (savedTheme === 'dark') {
    window.nahjulBalaghApp.currentTheme = 'light'; // Set opposite so toggle works
    window.nahjulBalaghApp.toggleTheme();
  }
});
