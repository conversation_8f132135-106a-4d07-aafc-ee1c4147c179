/* ===== THEME SYSTEM ===== */

/* Light Theme (Default) */
.light-theme {
  --bg-primary: #FEFEFE;
  --bg-secondary: #F8F6F0;
  --bg-tertiary: #FFFFFF;
  --text-primary: #2D2D2D;
  --text-secondary: #666666;
  --text-muted: #999999;
  --border-color: rgba(0, 0, 0, 0.1);
  --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Dark Theme */
.dark-theme {
  --bg-primary: #1A1A2E;
  --bg-secondary: #16213E;
  --bg-tertiary: #0F3460;
  --text-primary: #FEFEFE;
  --text-secondary: #E0E0E0;
  --text-muted: #B0B0B0;
  --border-color: rgba(255, 255, 255, 0.1);
  --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Apply theme colors */
body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.navbar {
  background: rgba(var(--bg-tertiary), 0.95);
  border-bottom: 1px solid var(--border-color);
}

.dark-theme .navbar {
  background: rgba(26, 26, 46, 0.95);
}

.nav-link {
  color: var(--text-primary);
}

.control-btn {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.about {
  background: var(--bg-secondary);
}

.categories {
  background: var(--bg-primary);
}

.category-card {
  background: var(--bg-tertiary);
  box-shadow: 0 4px 16px var(--shadow-color);
}

.stat-item {
  background: var(--bg-tertiary);
  box-shadow: 0 2px 8px var(--shadow-color);
}

/* Dark theme specific adjustments */
.dark-theme .hero-background {
  background: linear-gradient(135deg, var(--primary-blue) 0%, #0A0A1A 100%);
}

.dark-theme .quote-section {
  background: linear-gradient(135deg, var(--bg-tertiary), var(--bg-secondary));
}

.dark-theme .footer {
  background: #0A0A1A;
}

/* Theme transition */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Theme toggle button states */
.light-theme #theme-toggle i::before {
  content: "\f186"; /* sun icon */
}

.dark-theme #theme-toggle i::before {
  content: "\f185"; /* moon icon */
}

/* Scrollbar theming */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-gold);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-teal);
}

/* Selection colors */
::selection {
  background: var(--primary-gold);
  color: white;
}

::-moz-selection {
  background: var(--primary-gold);
  color: white;
}

/* Focus states for accessibility */
.nav-link:focus,
.btn:focus,
.control-btn:focus,
.action-btn:focus {
  outline: 2px solid var(--primary-gold);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-gold: #FFD700;
    --primary-blue: #000080;
    --text-primary: #000000;
  }
  
  .dark-theme {
    --text-primary: #FFFFFF;
    --bg-primary: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .islamic-pattern-bg {
    animation: none;
  }
  
  .loading-content .islamic-pattern {
    animation: none;
  }
}
