/* ===== CSS VARIABLES ===== */
:root {
  /* Islamic Color Palette */
  --primary-gold: #D4AF37;
  --primary-green: #0F5132;
  --primary-blue: #1B4D72;
  --accent-teal: #20B2AA;
  --warm-white: #FEFEFE;
  --soft-cream: #F8F6F0;
  --deep-navy: #1A1A2E;
  --charcoal: #2D2D2D;
  
  /* Typography */
  --font-arabic: '<PERSON>i', 'Scheherazade New', serif;
  --font-english: 'Inter', sans-serif;
  --font-urdu: '<PERSON><PERSON>', serif;
  
  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  --spacing-xxl: 4rem;
  
  /* Border Radius */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  
  /* Shadows */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* ===== RESET & BASE ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-english);
  line-height: 1.6;
  color: var(--charcoal);
  background-color: var(--warm-white);
  overflow-x: hidden;
  transition: all var(--transition-normal);
}

/* ===== TYPOGRAPHY ===== */
.arabic {
  font-family: var(--font-arabic);
  font-size: 1.2em;
  line-height: 1.8;
  direction: rtl;
  text-align: right;
}

.urdu {
  font-family: var(--font-urdu);
  direction: rtl;
  text-align: right;
}

.english {
  font-family: var(--font-english);
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
}

/* ===== UTILITY CLASSES ===== */
.hidden {
  display: none !important;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.section-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: var(--spacing-xl);
  color: var(--primary-blue);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-gold), var(--accent-teal));
  border-radius: 2px;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-english);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-gold), var(--accent-teal));
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);
}

.btn-secondary:hover {
  background: var(--primary-blue);
  color: white;
}

/* ===== LOADING SCREEN ===== */
#loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-blue), var(--deep-navy));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity var(--transition-slow);
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-content h2 {
  font-family: var(--font-arabic);
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
  color: var(--primary-gold);
}

.islamic-pattern {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-md);
  border: 3px solid var(--primary-gold);
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all var(--transition-normal);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-brand h1 {
  font-family: var(--font-arabic);
  font-size: 1.8rem;
  color: var(--primary-blue);
  margin: 0;
}

.nav-brand span {
  font-size: 0.9rem;
  color: var(--primary-gold);
  display: block;
  margin-top: -5px;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  color: var(--charcoal);
  text-decoration: none;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-blue);
  background: rgba(212, 175, 55, 0.1);
}

.nav-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.control-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-sm);
  background: var(--soft-cream);
  color: var(--charcoal);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: var(--primary-gold);
  color: white;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  cursor: pointer;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: var(--charcoal);
  border-radius: 2px;
  transition: all var(--transition-fast);
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--deep-navy) 100%);
}

.islamic-pattern-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(32, 178, 170, 0.1) 0%, transparent 50%);
  background-size: 200px 200px;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(2deg); }
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  text-align: center;
  color: white;
}

.hero-title .arabic {
  font-size: 4rem;
  font-weight: 700;
  color: var(--primary-gold);
  display: block;
  margin-bottom: var(--spacing-sm);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-title .english {
  font-size: 2rem;
  font-weight: 300;
  color: var(--warm-white);
  display: block;
}

.hero-subtitle {
  font-size: 1.2rem;
  margin: var(--spacing-lg) 0 var(--spacing-xl);
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

/* ===== ABOUT SECTION ===== */
.about {
  padding: var(--spacing-xxl) 0;
  background: var(--soft-cream);
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
  align-items: center;
}

.about-text p {
  font-size: 1.1rem;
  margin-bottom: var(--spacing-md);
  color: var(--charcoal);
  line-height: 1.8;
}

.about-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-lg);
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  color: var(--primary-gold);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 1.1rem;
  color: var(--primary-blue);
  font-weight: 500;
}

/* ===== QUOTE SECTION ===== */
.quote-section {
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(135deg, var(--primary-blue), var(--deep-navy));
  color: white;
}

.quote-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-lg);
}

.quote-content {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.quote-text .arabic {
  font-size: 1.8rem;
  color: var(--primary-gold);
  margin-bottom: var(--spacing-md);
  line-height: 1.8;
}

.quote-text .translation {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: var(--spacing-sm);
}

.quote-reference {
  font-size: 0.9rem;
  opacity: 0.7;
  font-style: italic;
}

.quote-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.action-btn {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.action-btn:hover {
  background: var(--primary-gold);
  transform: scale(1.1);
}

.action-btn.active {
  background: var(--primary-gold);
}

/* ===== CATEGORIES SECTION ===== */
.categories {
  padding: var(--spacing-xxl) 0;
  background: var(--warm-white);
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
}

.category-card {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
  transition: left var(--transition-slow);
}

.category-card:hover::before {
  left: 100%;
}

.category-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.category-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-md);
  background: linear-gradient(135deg, var(--primary-gold), var(--accent-teal));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
}

.category-card h3 {
  font-size: 1.5rem;
  color: var(--primary-blue);
  margin-bottom: var(--spacing-sm);
}

.category-card p {
  color: var(--charcoal);
  opacity: 0.8;
  margin-bottom: var(--spacing-md);
}

.category-count {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: var(--primary-gold);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

/* ===== FOOTER ===== */
.footer {
  background: var(--deep-navy);
  color: white;
  padding: var(--spacing-xxl) 0 var(--spacing-lg);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.footer-section h3,
.footer-section h4 {
  color: var(--primary-gold);
  margin-bottom: var(--spacing-md);
}

.footer-section p {
  opacity: 0.8;
  line-height: 1.6;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: var(--spacing-xs);
}

.footer-section ul li a {
  color: white;
  text-decoration: none;
  opacity: 0.8;
  transition: all var(--transition-fast);
}

.footer-section ul li a:hover {
  opacity: 1;
  color: var(--primary-gold);
}

.footer-bottom {
  text-align: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0.7;
}

/* ===== ANIMATIONS ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-50px);
  animation: slideInLeft 0.6s ease forwards;
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  opacity: 0;
  transform: translateX(50px);
  animation: slideInRight 0.6s ease forwards;
}

@keyframes slideInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
