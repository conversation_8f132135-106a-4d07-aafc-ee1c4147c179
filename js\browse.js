// Browse page functionality for Nahjul Balagha website

class BrowsePage {
  constructor() {
    this.currentView = 'grid';
    this.currentFilters = {
      type: 'all',
      category: 'all',
      sort: 'number'
    };
    this.currentContent = [];
    this.displayedContent = [];
    this.itemsPerPage = 12;
    this.currentPage = 1;
    this.bookmarks = this.loadBookmarks();
    this.selectedContent = null;

    this.init();
  }

  init() {
    this.loadAllContent();
    this.setupEventListeners();
    this.populateCategoryFilter();
    this.applyFilters();
    this.checkForPreselectedCategory();
  }

  setupEventListeners() {
    // Filter controls
    document.getElementById('type-filter').addEventListener('change', (e) => {
      this.currentFilters.type = e.target.value;
      this.applyFilters();
    });

    document.getElementById('category-filter').addEventListener('change', (e) => {
      this.currentFilters.category = e.target.value;
      this.applyFilters();
    });

    document.getElementById('sort-filter').addEventListener('change', (e) => {
      this.currentFilters.sort = e.target.value;
      this.applyFilters();
    });

    // Filter actions
    document.getElementById('clear-filters').addEventListener('click', () => {
      this.clearFilters();
    });

    document.getElementById('random-content').addEventListener('click', () => {
      this.showRandomContent();
    });

    // View toggle
    document.getElementById('grid-view').addEventListener('click', () => {
      this.setView('grid');
    });

    document.getElementById('list-view').addEventListener('click', () => {
      this.setView('list');
    });

    // Modal controls
    this.setupModalEventListeners();

    // Infinite scroll
    this.setupInfiniteScroll();
  }

  setupModalEventListeners() {
    const modal = document.getElementById('content-modal');
    const modalClose = document.getElementById('modal-close');
    const modalOverlay = modal.querySelector('.modal-overlay');

    modalClose.addEventListener('click', () => this.closeModal());
    modalOverlay.addEventListener('click', () => this.closeModal());

    // Modal actions
    document.getElementById('modal-bookmark').addEventListener('click', () => {
      this.toggleBookmark(this.selectedContent);
    });

    document.getElementById('modal-share').addEventListener('click', () => {
      this.shareContent(this.selectedContent);
    });

    document.getElementById('modal-audio').addEventListener('click', () => {
      this.playAudio(this.selectedContent);
    });

    document.getElementById('modal-copy').addEventListener('click', () => {
      this.copyContent(this.selectedContent);
    });

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (modal.classList.contains('hidden')) return;

      if (e.key === 'Escape') {
        this.closeModal();
      } else if (e.key === 'ArrowLeft') {
        this.showPreviousContent();
      } else if (e.key === 'ArrowRight') {
        this.showNextContent();
      }
    });
  }

  setupInfiniteScroll() {
    window.addEventListener('scroll', () => {
      if (this.isNearBottom() && this.hasMoreContent()) {
        this.loadMoreContent();
      }
    });
  }

  loadAllContent() {
    this.currentContent = DataHelper.getAllContent();
  }

  populateCategoryFilter() {
    const categoryFilter = document.getElementById('category-filter');
    const allCategories = new Set();

    this.currentContent.forEach(item => {
      allCategories.add(item.category);
    });

    // Clear existing options except "All Categories"
    while (categoryFilter.children.length > 1) {
      categoryFilter.removeChild(categoryFilter.lastChild);
    }

    // Add category options
    Array.from(allCategories).sort().forEach(category => {
      const option = document.createElement('option');
      option.value = category;
      option.textContent = this.formatCategoryName(category);
      categoryFilter.appendChild(option);
    });
  }

  formatCategoryName(category) {
    return category.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  }

  applyFilters() {
    let filteredContent = [...this.currentContent];

    // Filter by type
    if (this.currentFilters.type !== 'all') {
      filteredContent = filteredContent.filter(item => item.type === this.currentFilters.type);
    }

    // Filter by category
    if (this.currentFilters.category !== 'all') {
      filteredContent = filteredContent.filter(item => item.category === this.currentFilters.category);
    }

    // Sort content
    filteredContent.sort((a, b) => {
      switch (this.currentFilters.sort) {
        case 'number':
          return a.number - b.number;
        case 'type':
          return a.type.localeCompare(b.type);
        case 'category':
          return a.category.localeCompare(b.category);
        default:
          return 0;
      }
    });

    this.displayedContent = filteredContent;
    this.currentPage = 1;
    this.renderContent();
    this.updateContentStats();
  }

  renderContent() {
    const contentGrid = document.getElementById('content-grid');
    const startIndex = 0;
    const endIndex = this.currentPage * this.itemsPerPage;
    const contentToShow = this.displayedContent.slice(startIndex, endIndex);

    contentGrid.innerHTML = '';
    contentGrid.className = `content-grid ${this.currentView}-view`;

    if (contentToShow.length === 0) {
      this.showNoResults();
      return;
    }

    contentToShow.forEach(item => {
      const contentCard = this.createContentCard(item);
      contentGrid.appendChild(contentCard);
    });

    this.hideNoResults();
    this.animateContentCards();
  }

  createContentCard(item) {
    const card = document.createElement('div');
    card.className = 'content-card';
    card.dataset.id = item.id;

    const typeIcon = this.getTypeIcon(item.type);
    const isBookmarked = this.bookmarks.includes(item.id);

    card.innerHTML = `
      <div class="card-header">
        <div class="card-type">
          <i class="${typeIcon}"></i>
          <span class="type-text">${this.formatTypeName(item.type)}</span>
        </div>
        <div class="card-number">#${item.number}</div>
        <button class="bookmark-btn ${isBookmarked ? 'active' : ''}" data-id="${item.id}">
          <i class="${isBookmarked ? 'fas' : 'far'} fa-bookmark"></i>
        </button>
      </div>
      
      <div class="card-content">
        <div class="arabic-preview">${this.truncateText(item.arabic, 100)}</div>
        <div class="translation-preview">
          <p class="english">${this.truncateText(item.english, 150)}</p>
          <p class="urdu hidden">${this.truncateText(item.urdu, 150)}</p>
        </div>
      </div>
      
      <div class="card-footer">
        <span class="category-tag">${this.formatCategoryName(item.category)}</span>
        <div class="card-actions">
          <button class="action-btn share-btn" data-id="${item.id}" title="Share">
            <i class="fas fa-share-alt"></i>
          </button>
          <button class="action-btn audio-btn" data-id="${item.id}" title="Audio">
            <i class="fas fa-play"></i>
          </button>
        </div>
      </div>
    `;

    // Add event listeners
    card.addEventListener('click', (e) => {
      if (!e.target.closest('.bookmark-btn, .action-btn')) {
        this.openModal(item);
      }
    });

    card.querySelector('.bookmark-btn').addEventListener('click', (e) => {
      e.stopPropagation();
      this.toggleBookmark(item);
    });

    card.querySelector('.share-btn').addEventListener('click', (e) => {
      e.stopPropagation();
      this.shareContent(item);
    });

    card.querySelector('.audio-btn').addEventListener('click', (e) => {
      e.stopPropagation();
      this.playAudio(item);
    });

    return card;
  }

  getTypeIcon(type) {
    const icons = {
      sermon: 'fas fa-microphone',
      letter: 'fas fa-envelope',
      saying: 'fas fa-quote-right'
    };
    return icons[type] || 'fas fa-book';
  }

  formatTypeName(type) {
    const names = {
      sermon: 'Sermon',
      letter: 'Letter',
      saying: 'Saying'
    };
    return names[type] || type;
  }

  truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  }

  openModal(content) {
    this.selectedContent = content;
    const modal = document.getElementById('content-modal');

    // Populate modal content
    document.getElementById('modal-title').textContent = content.title || `${this.formatTypeName(content.type)} #${content.number}`;
    document.getElementById('modal-arabic').textContent = content.arabic;
    document.getElementById('modal-english').textContent = content.english;
    document.getElementById('modal-urdu').textContent = content.urdu;
    document.getElementById('modal-reference').textContent = `${this.formatTypeName(content.type)} #${content.number}`;
    document.getElementById('modal-category').textContent = this.formatCategoryName(content.category);

    // Update bookmark button
    const bookmarkBtn = document.getElementById('modal-bookmark');
    const isBookmarked = this.bookmarks.includes(content.id);
    bookmarkBtn.querySelector('i').className = isBookmarked ? 'fas fa-bookmark' : 'far fa-bookmark';
    bookmarkBtn.classList.toggle('active', isBookmarked);

    // Show modal
    modal.classList.remove('hidden');
    document.body.style.overflow = 'hidden';

    // Animate modal
    if (window.getAnimationManager && !window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      const animationManager = window.getAnimationManager();
      if (animationManager && animationManager.isGSAPLoaded) {
        gsap.from('.modal-content', {
          duration: 0.4,
          scale: 0.8,
          opacity: 0,
          ease: 'back.out(1.7)'
        });
      }
    }
  }

  closeModal() {
    const modal = document.getElementById('content-modal');
    modal.classList.add('hidden');
    document.body.style.overflow = '';
    this.selectedContent = null;
  }

  showPreviousContent() {
    if (!this.selectedContent) return;
    
    const currentIndex = this.displayedContent.findIndex(item => item.id === this.selectedContent.id);
    if (currentIndex > 0) {
      this.openModal(this.displayedContent[currentIndex - 1]);
    }
  }

  showNextContent() {
    if (!this.selectedContent) return;
    
    const currentIndex = this.displayedContent.findIndex(item => item.id === this.selectedContent.id);
    if (currentIndex < this.displayedContent.length - 1) {
      this.openModal(this.displayedContent[currentIndex + 1]);
    }
  }

  toggleBookmark(content) {
    const contentId = content.id;
    const isBookmarked = this.bookmarks.includes(contentId);

    if (isBookmarked) {
      this.bookmarks = this.bookmarks.filter(id => id !== contentId);
    } else {
      this.bookmarks.push(contentId);
    }

    this.saveBookmarks();
    this.updateBookmarkButtons(contentId, !isBookmarked);
    this.showNotification(isBookmarked ? 'Bookmark removed' : 'Content bookmarked');
  }

  updateBookmarkButtons(contentId, isBookmarked) {
    // Update card bookmark button
    const cardBookmarkBtn = document.querySelector(`.bookmark-btn[data-id="${contentId}"]`);
    if (cardBookmarkBtn) {
      const icon = cardBookmarkBtn.querySelector('i');
      icon.className = isBookmarked ? 'fas fa-bookmark' : 'far fa-bookmark';
      cardBookmarkBtn.classList.toggle('active', isBookmarked);
    }

    // Update modal bookmark button if this content is currently selected
    if (this.selectedContent && this.selectedContent.id === contentId) {
      const modalBookmarkBtn = document.getElementById('modal-bookmark');
      const icon = modalBookmarkBtn.querySelector('i');
      icon.className = isBookmarked ? 'fas fa-bookmark' : 'far fa-bookmark';
      modalBookmarkBtn.classList.toggle('active', isBookmarked);
    }
  }

  shareContent(content) {
    const shareText = `"${content.english}"\n\n- Imam Ali (a.s)\nNahjul Balagha, ${this.formatTypeName(content.type)} #${content.number}`;
    
    if (navigator.share) {
      navigator.share({
        title: `Nahjul Balagha - ${this.formatTypeName(content.type)} #${content.number}`,
        text: shareText,
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(shareText).then(() => {
        this.showNotification('Content copied to clipboard');
      });
    }
  }

  playAudio(content) {
    // Placeholder for audio functionality
    this.showNotification('Audio playback started (demo)');
  }

  copyContent(content) {
    const copyText = `${content.arabic}\n\n${content.english}\n\n${content.urdu}\n\n- Imam Ali (a.s), Nahjul Balagha ${this.formatTypeName(content.type)} #${content.number}`;
    
    navigator.clipboard.writeText(copyText).then(() => {
      this.showNotification('Content copied to clipboard');
    });
  }

  clearFilters() {
    this.currentFilters = {
      type: 'all',
      category: 'all',
      sort: 'number'
    };

    document.getElementById('type-filter').value = 'all';
    document.getElementById('category-filter').value = 'all';
    document.getElementById('sort-filter').value = 'number';

    this.applyFilters();
  }

  showRandomContent() {
    const randomContent = DataHelper.getRandomQuote();
    this.openModal(randomContent);
  }

  setView(viewType) {
    this.currentView = viewType;
    
    document.getElementById('grid-view').classList.toggle('active', viewType === 'grid');
    document.getElementById('list-view').classList.toggle('active', viewType === 'list');
    
    this.renderContent();
  }

  updateContentStats() {
    const contentCount = document.getElementById('content-count');
    const count = this.displayedContent.length;
    const total = this.currentContent.length;
    
    const englishText = count === total ? `Showing all ${count} items` : `Showing ${count} of ${total} items`;
    const urduText = count === total ? `تمام ${count} آئٹمز دکھائے جا رہے ہیں` : `${total} میں سے ${count} آئٹمز دکھائے جا رہے ہیں`;
    
    contentCount.querySelector('.english').textContent = englishText;
    contentCount.querySelector('.urdu').textContent = urduText;
  }

  showNoResults() {
    document.getElementById('no-results').classList.remove('hidden');
  }

  hideNoResults() {
    document.getElementById('no-results').classList.add('hidden');
  }

  isNearBottom() {
    return window.innerHeight + window.scrollY >= document.body.offsetHeight - 1000;
  }

  hasMoreContent() {
    return this.currentPage * this.itemsPerPage < this.displayedContent.length;
  }

  loadMoreContent() {
    if (this.hasMoreContent()) {
      this.currentPage++;
      this.renderContent();
    }
  }

  animateContentCards() {
    if (window.getAnimationManager && !window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      const animationManager = window.getAnimationManager();
      if (animationManager && animationManager.isGSAPLoaded) {
        gsap.from('.content-card', {
          duration: 0.6,
          y: 50,
          opacity: 0,
          stagger: 0.1,
          ease: 'power2.out'
        });
      }
    }
  }

  checkForPreselectedCategory() {
    const selectedCategory = localStorage.getItem('nahjul-balagha-selected-category');
    if (selectedCategory) {
      const typeMap = {
        sermons: 'sermons',
        letters: 'letters',
        sayings: 'sayings'
      };
      
      if (typeMap[selectedCategory]) {
        this.currentFilters.type = typeMap[selectedCategory];
        document.getElementById('type-filter').value = typeMap[selectedCategory];
        this.applyFilters();
      }
      
      localStorage.removeItem('nahjul-balagha-selected-category');
    }
  }

  loadBookmarks() {
    const saved = localStorage.getItem('nahjul-balagha-bookmarks');
    return saved ? JSON.parse(saved) : [];
  }

  saveBookmarks() {
    localStorage.setItem('nahjul-balagha-bookmarks', JSON.stringify(this.bookmarks));
  }

  showNotification(message) {
    // Reuse notification system from main.js
    if (window.nahjulBalaghApp) {
      window.nahjulBalaghApp.showNotification(message);
    }
  }
}

// Initialize browse page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.browsePage = new BrowsePage();
});
