// Theme management for Nahjul Balagha website

class ThemeManager {
  constructor() {
    this.themes = {
      light: {
        name: 'Light',
        icon: 'fas fa-sun',
        colors: {
          primary: '#1B4D72',
          secondary: '#D4AF37',
          accent: '#20B2AA',
          background: '#FEFEFE',
          surface: '#FFFFFF',
          text: '#2D2D2D',
          textSecondary: '#666666'
        }
      },
      dark: {
        name: 'Dark',
        icon: 'fas fa-moon',
        colors: {
          primary: '#4A90E2',
          secondary: '#FFD700',
          accent: '#40E0D0',
          background: '#1A1A2E',
          surface: '#16213E',
          text: '#FEFEFE',
          textSecondary: '#E0E0E0'
        }
      },
      sepia: {
        name: 'Sepia',
        icon: 'fas fa-book',
        colors: {
          primary: '#8B4513',
          secondary: '#CD853F',
          accent: '#DEB887',
          background: '#F5F5DC',
          surface: '#FFF8DC',
          text: '#654321',
          textSecondary: '#8B7355'
        }
      },
      highContrast: {
        name: 'High Contrast',
        icon: 'fas fa-adjust',
        colors: {
          primary: '#000000',
          secondary: '#FFFF00',
          accent: '#00FFFF',
          background: '#FFFFFF',
          surface: '#F0F0F0',
          text: '#000000',
          textSecondary: '#333333'
        }
      }
    };
    
    this.currentTheme = this.loadSavedTheme();
    this.applyTheme(this.currentTheme);
  }

  loadSavedTheme() {
    const saved = localStorage.getItem('nahjul-balagha-theme');
    return saved && this.themes[saved] ? saved : 'light';
  }

  saveTheme(themeName) {
    localStorage.setItem('nahjul-balagha-theme', themeName);
  }

  applyTheme(themeName) {
    if (!this.themes[themeName]) return;

    const theme = this.themes[themeName];
    const root = document.documentElement;

    // Apply CSS custom properties
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--theme-${key}`, value);
    });

    // Update body class
    document.body.className = document.body.className.replace(/\w+-theme/g, '');
    document.body.classList.add(`${themeName}-theme`);

    // Update theme toggle button if it exists
    this.updateThemeToggleButton(theme);

    this.currentTheme = themeName;
    this.saveTheme(themeName);

    // Dispatch theme change event
    window.dispatchEvent(new CustomEvent('themeChanged', {
      detail: { theme: themeName, colors: theme.colors }
    }));
  }

  updateThemeToggleButton(theme) {
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
      const icon = themeToggle.querySelector('i');
      if (icon) {
        icon.className = theme.icon;
      }
      themeToggle.setAttribute('aria-label', `Switch to ${theme.name} theme`);
      themeToggle.setAttribute('title', `Current: ${theme.name} theme`);
    }
  }

  cycleTheme() {
    const themeNames = Object.keys(this.themes);
    const currentIndex = themeNames.indexOf(this.currentTheme);
    const nextIndex = (currentIndex + 1) % themeNames.length;
    const nextTheme = themeNames[nextIndex];
    
    this.applyTheme(nextTheme);
    return nextTheme;
  }

  getThemeList() {
    return Object.entries(this.themes).map(([key, theme]) => ({
      key,
      name: theme.name,
      icon: theme.icon,
      current: key === this.currentTheme
    }));
  }

  // Auto theme based on system preference
  enableAutoTheme() {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = (e) => {
        const systemTheme = e.matches ? 'dark' : 'light';
        this.applyTheme(systemTheme);
      };

      mediaQuery.addListener(handleChange);
      handleChange(mediaQuery); // Apply initial theme
      
      return () => mediaQuery.removeListener(handleChange);
    }
  }

  // Theme-specific animations
  applyThemeTransitions() {
    const style = document.createElement('style');
    style.textContent = `
      * {
        transition: 
          background-color 0.3s ease,
          color 0.3s ease,
          border-color 0.3s ease,
          box-shadow 0.3s ease !important;
      }
      
      .theme-transition-disabled * {
        transition: none !important;
      }
    `;
    document.head.appendChild(style);
  }

  // Accessibility features
  setupAccessibilityFeatures() {
    // Respect user's motion preferences
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      document.body.classList.add('reduced-motion');
    }

    // High contrast mode detection
    if (window.matchMedia('(prefers-contrast: high)').matches) {
      this.applyTheme('highContrast');
    }

    // Font size preferences
    const fontSize = localStorage.getItem('nahjul-balagha-font-size');
    if (fontSize) {
      document.documentElement.style.fontSize = fontSize;
    }
  }

  // Font size controls
  increaseFontSize() {
    const currentSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
    const newSize = Math.min(currentSize + 2, 24);
    document.documentElement.style.fontSize = `${newSize}px`;
    localStorage.setItem('nahjul-balagha-font-size', `${newSize}px`);
  }

  decreaseFontSize() {
    const currentSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
    const newSize = Math.max(currentSize - 2, 12);
    document.documentElement.style.fontSize = `${newSize}px`;
    localStorage.setItem('nahjul-balagha-font-size', `${newSize}px`);
  }

  resetFontSize() {
    document.documentElement.style.fontSize = '16px';
    localStorage.removeItem('nahjul-balagha-font-size');
  }

  // Print-friendly theme
  enablePrintMode() {
    const printStyles = `
      @media print {
        * {
          background: white !important;
          color: black !important;
          box-shadow: none !important;
        }
        
        .navbar, .footer, .hero-buttons, .quote-actions {
          display: none !important;
        }
        
        .quote-text .arabic {
          color: black !important;
          font-size: 18pt !important;
        }
        
        .quote-text .translation {
          color: #333 !important;
          font-size: 14pt !important;
        }
      }
    `;
    
    const style = document.createElement('style');
    style.textContent = printStyles;
    document.head.appendChild(style);
  }
}

// Initialize theme manager
let themeManager;

document.addEventListener('DOMContentLoaded', () => {
  themeManager = new ThemeManager();
  themeManager.applyThemeTransitions();
  themeManager.setupAccessibilityFeatures();
  themeManager.enablePrintMode();
});

// Export for global access
window.ThemeManager = ThemeManager;
window.getThemeManager = () => themeManager;
